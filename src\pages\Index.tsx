import React, { useState, useEffect } from 'react';
import { ChevronDown, Menu, X, MapPin, Phone, Mail, Users, Award, Globe, Building, MessageCircle } from 'lucide-react';
import { Link } from 'react-router-dom';
import Layout from "@/components/layout/Layout";
import ClientLogosSection from "@/components/ClientLogosSection";

const Krykard = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeProductTab, setActiveProductTab] = useState('power');
  const [currentSlide, setCurrentSlide] = useState(0);
  const [showContactModal, setShowContactModal] = useState(false);
  const [animatedStats, setAnimatedStats] = useState({
    manufacturer: 0,
    service: 0,
    supplier: 0,
    certified: 0,
    experience: 0
  });

  const bannerImages = [
    '/background_images/Slide_1.png',
    '/background_images/Slide_2.png',
    '/background_images/Slide_3.png',
    '/background_images/Slide_4.png',
    '/background_images/Slide_5.png',

  ];

  const stats = [
    { key: 'manufacturer', value: 1, label: 'MANUFACTURER OF SERVO STABILISER', icon: Award },
    { key: 'service', value: 100, label: 'SERVICE CENTRES', icon: Building },
    { key: 'supplier', value: 0, label: 'PREFERRED SUPPLIER OF LARGE CORPORATES & OEM\'S', icon: Users },
    { key: 'certified', value: 0, label: 'CE CERTIFIED PRODUCTS', icon: Award },
    { key: 'experience', value: 40, label: 'YEARS EXPERIENCE', icon: Globe }
  ];

  const productTabs = {
    power: {
      title: 'Power Segment',
      products: [
        { name: 'Power Transformer', image: '/background_images/product-thumb.jpg' },
        { name: 'Distribution Transformer', image: '/background_images/product-thumb.jpg' },
        { name: 'Furnace Transformer', image: '/background_images/product-thumb.jpg' },
        { name: 'Solar Transformer', image: '/background_images/product-thumb.jpg' },
        { name: 'Hermetically Sealed Transformer', image: '/background_images/product-thumb.jpg' },
        { name: 'Compact Sub Station', image: '/background_images/product-thumb.jpg' }
      ]
    },
    renewable: {
      title: 'Renewable Energy Segment',
      products: [
        { name: 'Solar Power Systems', image: '/background_images/product-thumb.jpg' },
        { name: 'Solar Inverters', image: '/background_images/product-thumb.jpg' },
        { name: 'Solar Batteries', image: '/background_images/product-thumb.jpg' },
        { name: 'Solar Panels', image: '/background_images/product-thumb.jpg' }
      ]
    },
    conditioning: {
      title: 'Power Conditioning Segment',
      products: [
        { name: 'Servo Voltage Stabilizers', image: '/background_images/product-thumb.jpg' },
        { name: 'Rolling Contact Servo Stabilizers', image: '/background_images/product-thumb.jpg' },
        { name: 'Online UPS', image: '/background_images/product-thumb.jpg' },
        { name: 'CVT', image: '/background_images/product-thumb.jpg' }
      ]
    },
    domestic: {
      title: 'Domestic Network Segment',
      products: [
        { name: 'Automatic Voltage Stabilizers', image: '/background_images/product-thumb.jpg' },
        { name: 'Water Heater/Geyser', image: '/background_images/product-thumb.jpg' }
      ]
    }
  };

  const productCategories = [
    {
      title: 'Measure',
      image: '/background_images/test and measurement.jpg',
      description: 'Advanced measurement tools and equipment for precise diagnostics',
      redirectUrl: '/measure'
    },
    {
      title: 'Online UPS',
      image: 'https://www.j-schneider.de/fileadmin/_processed_/4/8/csm_USV_1ad6803997.jpg',
      description: 'Reliable uninterrupted power supply solutions for critical applications',
      redirectUrl: '/protect/ups'
    },
    {
      title: 'Power Conditioners',
      image: '/StaticVoltageRegulator (1).jpg',
      description: 'Advanced servo stabilizers and voltage regulation systems',
      redirectUrl: '/protect/servo-stabilizers'
    },
    {
      title: 'Static Voltage Regulators',
      image: 'https://spectronstabilizer.com/wp-content/uploads/2021/03/About-US-4.jpg',
      description: 'High-performance voltage regulation for critical applications',
      redirectUrl: '/protect/static-stabilizers'
    }
  ];

  // Animate stats on component mount
  useEffect(() => {
    const animateStats = () => {
      const duration = 2000;
      const increment = 50;
      const steps = duration / increment;

      stats.forEach(stat => {
        let current = 0;
        const stepValue = stat.value / steps;

        const timer = setInterval(() => {
          current += stepValue;
          if (current >= stat.value) {
            current = stat.value;
            clearInterval(timer);
          }
          setAnimatedStats(prev => ({
            ...prev,
            [stat.key]: Math.floor(current)
          }));
        }, increment);
      });
    };

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          animateStats();
          observer.disconnect();
        }
      },
      { threshold: 0.5 }
    );

    const statsElement = document.getElementById('stats-section');
    if (statsElement) {
      observer.observe(statsElement);
    }

    return () => observer.disconnect();
  }, []);

  // Auto-slide carousel
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % bannerImages.length);
    }, 5000);

    return () => clearInterval(timer);
  }, [bannerImages.length]);

  const HeroCarousel = () => (
    <div className="relative w-full overflow-hidden prevent-overflow mt-[60px] sm:mt-[64px] md:mt-[68px] lg:mt-[72px] xl:mt-[76px] height-responsive-screen">
      <div
        className="flex transition-transform duration-500 ease-in-out h-full w-full"
        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
      >
        {bannerImages.map((image, index) => (
          <div key={index} className="w-full h-full flex-shrink-0 relative bg-black overflow-hidden">
            <img
              src={image}
              alt={`Banner ${index + 1}`}
              className="img-full-cover img-responsive img-enhanced"
              style={{
                objectFit: 'cover',
                objectPosition: 'center center',
                minWidth: '100%',
                minHeight: '100%',
                imageRendering: 'crisp-edges'
              }}
              loading={index === 0 ? "eager" : "lazy"}
            />
            {/* Enhanced overlay for better visual prominence */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none"></div>
            {/* Additional subtle vignette effect */}
            <div className="absolute inset-0 bg-radial-gradient from-transparent via-transparent to-black/15 pointer-events-none"></div>
          </div>
        ))}
      </div>

      {/* Carousel indicators - Enhanced responsive touch targets */}
      <div className="absolute bottom-4 sm:bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-1 sm:space-x-2 z-10">
        {bannerImages.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`touch-target w-2 h-2 sm:w-2.5 sm:h-2.5 rounded-full transition-all duration-300 ${
              currentSlide === index ? 'bg-white scale-110 shadow-lg' : 'bg-white/50 hover:bg-white/70'
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      {/* Navigation arrows - Enhanced responsive touch targets */}
      <button
        onClick={() => setCurrentSlide(prev => (prev - 1 + bannerImages.length) % bannerImages.length)}
        className="absolute left-2 sm:left-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white touch-target-lg rounded-full hover:bg-black/70 transition-all duration-300 z-10 shadow-lg"
        aria-label="Previous slide"
      >
        <span className="text-sm sm:text-base font-bold">‹</span>
      </button>
      <button
        onClick={() => setCurrentSlide(prev => (prev + 1) % bannerImages.length)}
        className="absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white touch-target-lg rounded-full hover:bg-black/70 transition-all duration-300 z-10 shadow-lg"
        aria-label="Next slide"
      >
        <span className="text-sm sm:text-base font-bold">›</span>
      </button>
    </div>
  );

  const StatsSection = () => (
    <div id="stats-section" className="relative padding-responsive-lg w-full prevent-overflow">
      {/* Background image with transparency */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat z-0"
        style={{ backgroundImage: 'url("https://cdn.pixabay.com/photo/2016/09/30/18/28/substation-1705950_1280.jpg")' }}
      ></div>

      {/* Dark overlay for better text readability */}
      <div className="absolute inset-0 bg-gradient-to-r from-gray-800/90 to-gray-900/90 z-0"></div>

      {/* Content - Responsive design */}
      <div className="relative z-10 w-full container-responsive">
        <div className="spacing-responsive-md text-center">
          <div className="relative inline-block mb-3">
            <h2 className="text-responsive-xl font-bold text-blue-400 font-['Open_Sans'] relative z-10">
              Our Legacy
            </h2>
            {/* Compact decorative underline */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-blue-400 rounded-full"></div>
          </div>

          {/* Compact subtitle */}
          <div className="flex items-center justify-center gap-3 mt-2">
            <div className="h-px w-12 bg-blue-300"></div>
            <span className="text-responsive-xs font-medium text-blue-300 tracking-wide uppercase font-['Open_Sans']">
              40+ Years Excellence
            </span>
            <div className="h-px w-12 bg-blue-300"></div>
          </div>
        </div>
        <div className="flex-responsive-col w-full items-stretch">
          {/* Left Content - Mobile First Design */}
          <div className="w-full lg:w-1/2 flex flex-col justify-center min-height-responsive padding-responsive-sm">
            <div className="text-white spacing-responsive-md">
              <p className="text-responsive-base font-medium text-white leading-relaxed font-['Open_Sans'] text-justify break-words-safe">
                Atandra Energy Pvt. Ltd., headquartered in Chennai, draws
                upon a rich foundation of more than 40+ years of expertise in
                the realm of Power & Energy Management.
              </p>
              <div>
                <h3 className="text-responsive-lg font-bold text-white font-['Open_Sans'] break-words-safe">
                  Sustainability Commitment
                </h3>
                <p className="text-responsive-base font-medium text-white/90 leading-relaxed font-['Open_Sans'] text-justify break-words-safe">
                  State-of-the-art facilities empower us to address the
                  requirements of Indian industries comprehensively, effectively
                  & efficiently, ensuring they derive maximum benefits from the
                  power conditioning & energy management solutions we provide.
                </p>
              </div>
            </div>
          </div>

          {/* Right Content - Responsive Stats Grid */}
          <div className="w-full lg:w-1/2 flex flex-col justify-center min-height-responsive padding-responsive-sm">
            <div className="grid-responsive-2-3-4 h-full">
              {/* Row 1 */}
              <div className="text-center padding-responsive-xs flex flex-col justify-center min-height-responsive">
                <div className="text-white text-responsive-lg mb-1">🏆</div>
                <div className="text-white text-responsive-lg font-bold font-['Open_Sans'] mb-1">INDIA'S NO.1</div>
                <h4 className="text-white text-responsive-xs font-semibold font-['Open_Sans'] leading-tight break-words-safe">MANUFACTURER OF SERVO</h4>
                <h4 className="text-white text-responsive-xs font-semibold font-['Open_Sans'] leading-tight break-words-safe">STABILISERS</h4>
              </div>

              <div className="text-center padding-responsive-xs flex flex-col justify-center min-height-responsive">
                <div className="text-white text-responsive-lg mb-1">🏢</div>
                <div className="text-white text-responsive-lg font-bold font-['Open_Sans'] mb-1">100+</div>
                <h4 className="text-white text-responsive-xs font-semibold font-['Open_Sans'] leading-tight break-words-safe">SERVICE CENTRES</h4>
              </div>

              <div className="text-center padding-responsive-xs flex flex-col justify-center min-height-responsive col-span-2 sm:col-span-1">
                <div className="text-white text-responsive-lg mb-1">👥</div>
                <div className="text-white text-responsive-sm font-bold font-['Open_Sans'] mb-1">PREFERRED</div>
                <h4 className="text-white text-responsive-xs font-semibold font-['Open_Sans'] leading-tight break-words-safe">PREFERRED SUPPLIER OF LARGE</h4>
                <h4 className="text-white text-responsive-xs font-semibold font-['Open_Sans'] leading-tight break-words-safe">CORPORATES & OEM'S</h4>
              </div>

              {/* Row 2 */}
              <div className="text-center padding-responsive-xs flex flex-col justify-center min-height-responsive">
                <div className="text-white text-responsive-lg mb-1">👨‍🔧</div>
                <div className="text-white text-responsive-lg font-bold font-['Open_Sans'] mb-1">CE</div>
                <h4 className="text-white text-responsive-xs font-semibold font-['Open_Sans'] leading-tight break-words-safe">CE CERTIFIED PRODUCTS</h4>
              </div>

              <div className="text-center padding-responsive-xs flex flex-col justify-center min-height-responsive">
                <div className="text-white text-responsive-lg mb-1">⭐</div>
                <div className="text-white text-responsive-lg font-bold font-['Open_Sans'] mb-1">40+</div>
                <h4 className="text-white text-responsive-xs font-semibold font-['Open_Sans'] leading-tight break-words-safe">YEARS</h4>
                <h4 className="text-white text-responsive-xs font-semibold font-['Open_Sans'] leading-tight break-words-safe">EXPERIENCE</h4>
              </div>

              <div className="text-center padding-responsive-xs flex flex-col justify-center min-height-responsive">
                <div className="text-white text-responsive-lg mb-1">🌍</div>
                <div className="text-white text-responsive-lg font-bold font-['Open_Sans'] mb-1">ISO</div>
                <h4 className="text-white text-responsive-xs font-semibold font-['Open_Sans'] leading-tight break-words-safe">ISO CERTIFIED</h4>
                <h4 className="text-white text-responsive-xs font-semibold font-['Open_Sans'] leading-tight break-words-safe">QUALITY STANDARDS</h4>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const ProductCategories = () => (
    <div className="pt-8 sm:pt-12 lg:pt-16 pb-6 sm:pb-8 lg:pb-10 bg-white w-full overflow-hidden">
      <div className="container-responsive">
        <div className="mb-6 sm:mb-8 lg:mb-10 text-center">
          <h2 className="text-responsive-2xl font-bold text-gray-800 mb-4 font-['Open_Sans']">Our Product Categories</h2>
          <div className="w-20 sm:w-24 md:w-32 h-1 bg-gray-600 mx-auto"></div>
        </div>
        <div className="grid-responsive-1-2 gap-4 sm:gap-6 lg:gap-8 w-full">
          {productCategories.map((category, index) => (
            <div key={index} className="rounded-xl overflow-hidden shadow-xl bg-white transform transition-all duration-300 hover:scale-[1.03] hover:shadow-2xl no-overlap border border-gray-100 flex flex-col">
              <div className="relative h-56 sm:h-72 md:h-80 lg:h-96 bg-gray-200 overflow-hidden">
                <img
                  src={category.image}
                  alt={category.title}
                  className="img-full-cover transform transition-transform duration-700 hover:scale-110"
                  style={{
                    objectFit: 'cover',
                    objectPosition: 'center center',
                    aspectRatio: '16/10',
                    imageRendering: 'crisp-edges'
                  }}
                  loading="lazy"
                />
                {/* Enhanced gradient overlay for better text readability */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                {/* Subtle corner vignette */}
                <div className="absolute inset-0 bg-radial-gradient from-transparent via-transparent to-black/10"></div>
              </div>
              <div className="p-4 sm:p-5 md:p-6 flex flex-col flex-grow">
                <h3 className="text-responsive-lg font-bold text-gray-800 mb-2 font-['Open_Sans']">{category.title}</h3>
                <p className="text-responsive-sm text-gray-600 leading-relaxed flex-grow font-['Open_Sans']">{category.description}</p>
                <div className="mt-3 sm:mt-4">
                  <Link to={category.redirectUrl} className="inline-block w-full sm:w-auto">
                    <button className="bg-gray-700 hover:bg-gray-800 text-white touch-target rounded text-sm font-medium transition-colors w-full sm:w-auto px-4 sm:px-6 font-['Open_Sans']">
                      Know More
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const CompanyStory = () => (
    <div className="py-12 sm:py-16 lg:py-20 bg-white w-full overflow-hidden">
      <div className="container-responsive">
        {/* Header Section */}
        <div className="text-center mb-8 sm:mb-12">
          <div className="relative inline-block mb-4 sm:mb-6">
            <h2 className="text-responsive-2xl font-bold font-['Open_Sans'] text-blue-600 relative z-10 pb-3">
              Network of Trust
            </h2>
            {/* Decorative underline */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-blue-600 rounded-full"></div>
          </div>

          {/* Subtitle with decorative line */}
          <div className="flex items-center justify-center gap-4 mb-6">
            <div className="h-px w-16 bg-blue-300"></div>
            <span className="text-sm font-semibold text-blue-600 tracking-wider uppercase font-['Open_Sans']">
              Nationwide Service Excellence
            </span>
            <div className="h-px w-16 bg-blue-300"></div>
          </div>

          <p className="text-gray-800 text-responsive-base leading-relaxed font-medium font-['Open_Sans'] max-w-4xl mx-auto text-justify">
            We built this nationwide footprint so you can focus on running your business, not chasing service calls. Fast response times, genuine parts, and expert technicians are just a phone call away—no matter how spread out your operations might be.
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 sm:gap-10 lg:gap-12 items-center">

          {/* Left Column - Map and Stats */}
          <div className="lg:col-span-1 space-y-8">
            {/* Enhanced India Map Container - Larger and More Prominent */}
            <div className="relative w-full bg-gradient-to-br from-blue-50 via-white to-indigo-100 rounded-2xl shadow-2xl overflow-hidden border-2 border-blue-200 transform transition-all duration-300 hover:scale-[1.02] hover:shadow-3xl">
              {/* Decorative border effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-transparent to-indigo-400/20 rounded-2xl"></div>
              <div className="relative p-4 sm:p-6">
                <img
                  src="/background_images/Service-Locations-India.jpeg"
                  alt="India Service Locations Map - Krykard Network Coverage"
                  className="w-full h-auto rounded-xl shadow-lg transform transition-transform duration-500 hover:scale-105"
                  style={{
                    minHeight: '400px',
                    maxHeight: '600px',
                    aspectRatio: '4/3',
                    objectFit: 'contain',
                    objectPosition: 'center center',
                    imageRendering: 'crisp-edges',
                    filter: 'contrast(1.1) saturate(1.1)'
                  }}
                  loading="eager"
                  onError={(e) => {
                    console.error('India map failed to load:', e);
                    const fallback = e.currentTarget.parentElement?.querySelector('.map-fallback');
                    if (fallback) {
                      fallback.classList.remove('hidden');
                      e.currentTarget.style.display = 'none';
                    }
                  }}
                />
                {/* Subtle overlay for enhanced visual appeal */}
                <div className="absolute inset-4 sm:inset-6 bg-gradient-to-t from-blue-50/30 to-transparent rounded-xl pointer-events-none"></div>
              </div>
              {/* Fallback content if image fails to load */}
              <div className="map-fallback hidden absolute inset-0 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 text-gray-600">
                <div className="text-center padding-responsive-lg">
                  <MapPin className="h-20 w-20 mx-auto mb-4 text-blue-400" />
                  <p className="text-xl font-bold text-blue-800 font-['Open_Sans']">India Service Network Map</p>
                  <p className="text-base font-medium text-blue-600 font-['Open_Sans']">100+ Service Centers Nationwide</p>
                  <p className="text-sm text-blue-500 mt-2 font-['Open_Sans']">Map temporarily unavailable</p>
                </div>
              </div>
            </div>

            {/* Enhanced Quick Stats Cards */}
            <div className="grid grid-cols-2 gap-4 sm:gap-6">
              <div className="bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 rounded-xl p-4 sm:p-6 text-center border-2 border-blue-300 shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
                <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-blue-600 mb-2 font-['Open_Sans']">100+</div>
                <div className="text-xs sm:text-sm font-semibold text-blue-800 font-['Open_Sans'] leading-tight">Service Centers</div>
              </div>
              <div className="bg-gradient-to-br from-green-50 via-green-100 to-green-200 rounded-xl p-4 sm:p-6 text-center border-2 border-green-300 shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
                <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-green-600 mb-2 font-['Open_Sans']">24/7</div>
                <div className="text-xs sm:text-sm font-semibold text-green-800 font-['Open_Sans'] leading-tight">Support Available</div>
              </div>
              <div className="bg-gradient-to-br from-purple-50 via-purple-100 to-purple-200 rounded-xl p-4 sm:p-6 text-center border-2 border-purple-300 shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
                <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-purple-600 mb-2 font-['Open_Sans']">24hrs</div>
                <div className="text-xs sm:text-sm font-semibold text-purple-800 font-['Open_Sans'] leading-tight">Response Time</div>
              </div>
              <div className="bg-gradient-to-br from-orange-50 via-orange-100 to-orange-200 rounded-xl p-4 sm:p-6 text-center border-2 border-orange-300 shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
                <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-orange-600 mb-2 font-['Open_Sans']">100%</div>
                <div className="text-xs sm:text-sm font-semibold text-orange-800 font-['Open_Sans'] leading-tight">Genuine Parts</div>
              </div>
            </div>
          </div>

          {/* Right Column - Key Highlights */}
          <div className="lg:col-span-2 font-['Open_Sans']">
            <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200 shadow-lg">
              <div className="text-center lg:text-left mb-6">
                <h3 className="text-responsive-xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent font-['Open_Sans'] mb-2">
                  Service Excellence
                </h3>
                <div className="flex items-center justify-center lg:justify-start gap-3">
                  <div className="h-1 w-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full"></div>
                  <span className="text-responsive-lg font-semibold text-gray-700 font-['Open_Sans']">Across India</span>
                  <div className="h-1 w-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"></div>
                </div>
              </div>
              <p className="text-gray-800 text-responsive-base leading-relaxed font-medium font-['Open_Sans'] mb-6 text-justify">
                From snow-clad valleys to sun-baked coastlines, from bustling metros to emerging industrial hubs, every dot means a certified engineer is on standby, ready to deliver rapid UPS and stabilizer support.
              </p>
            </div>

            {/* Split highlights into responsive columns */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
              {/* Column 1 */}
              <div className="space-y-4">
                <div className="flex items-start">
                  <span className="text-red-500 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">🚩</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-red-600">Nationwide Coverage:</strong> <span className="text-justify">100+ centers across every state and key industrial region.</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <span className="text-yellow-500 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">⚡</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-yellow-600">24-Hour Response:</strong> <span className="text-justify">Guaranteed on-site support within 24 hours, 365 days a year.</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <span className="text-blue-600 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">👨‍🔧</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-blue-600">Certified Technicians:</strong> <span className="text-justify">Local engineers trained on latest UPS technologies.</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <span className="text-gray-600 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">🛠️</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-gray-600">Stocked Inventory:</strong> <span className="text-justify">Genuine replacement parts available at every center.</span>
                  </span>
                </div>
              </div>

              {/* Column 2 */}
              <div className="space-y-4">
                <div className="flex items-start">
                  <span className="text-blue-500 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">🌐</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-blue-500">Remote Monitoring:</strong> <span className="text-justify">Proactive system health checks with real-time alerts.</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <span className="text-green-600 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">📞</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-green-600">24/7 Helpline:</strong> <span className="text-justify">Toll-free support line for instant troubleshooting.</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <span className="text-purple-600 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">🔄</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-purple-600">Preventive Maintenance:</strong> <span className="text-justify">Scheduled inspections to minimize downtime.</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <span className="text-indigo-600 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">📆</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-indigo-600">Easy Scheduling:</strong> <span className="text-justify">Book service visits online or via mobile app.</span>
                  </span>
                </div>
                <div className="flex items-start">
                  <span className="text-orange-600 text-lg sm:text-xl mr-3 flex-shrink-0 mt-1">🔒</span>
                  <span className="text-gray-800 text-responsive-sm font-medium font-['Open_Sans'] leading-relaxed">
                    <strong className="text-orange-600">Warranty Support:</strong> <span className="text-justify">Comprehensive warranty fulfillment and extensions.</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );



  const ContactModal = () => (
    showContactModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center padding-responsive-sm overflow-hidden">
        <div className="bg-white rounded-lg max-w-md w-full mx-4 padding-responsive-md max-h-[90vh] overflow-y-auto no-overlap shadow-2xl">
          <div className="flex justify-between items-center mb-4 sm:mb-6">
            <h3 className="text-responsive-lg font-semibold font-['Open_Sans']">Quick Enquiry</h3>
            <button
              onClick={() => setShowContactModal(false)}
              className="text-gray-400 hover:text-gray-600 touch-target rounded-md"
              aria-label="Close modal"
            >
              <X className="h-5 w-5 sm:h-6 sm:w-6" />
            </button>
          </div>
          <div className="spacing-responsive-sm">
            <input
              type="text"
              placeholder="Your Name"
              className="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 text-base touch-manipulation font-['Open_Sans']"
            />
            <input
              type="email"
              placeholder="Email Address"
              className="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 text-base touch-manipulation font-['Open_Sans']"
            />
            <input
              type="tel"
              placeholder="Phone Number"
              className="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 text-base touch-manipulation font-['Open_Sans']"
            />
            <textarea
              placeholder="Your Message"
              rows={4}
              className="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 text-base resize-none touch-manipulation font-['Open_Sans']"
            />
            <button
              onClick={() => setShowContactModal(false)}
              className="w-full bg-gray-600 hover:bg-gray-700 text-white touch-target rounded-md transition-colors text-base font-medium font-['Open_Sans']"
            >
              Send Enquiry
            </button>
          </div>
        </div>
      </div>
    )
  );

  return (
    <Layout>
      <div className="min-h-screen">
        <HeroCarousel />
        <ProductCategories />
        <StatsSection />
        <CompanyStory />
        <ClientLogosSection isInView={true} />
        <ContactModal />
      </div>
    </Layout>
  );
};

export default Krykard;