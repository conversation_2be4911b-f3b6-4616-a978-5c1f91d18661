import React, { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { useNavigate, useLocation } from 'react-router-dom';
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  ChevronRight,
  ChevronLeft,
  FileText,
  Mail,
  ArrowRight,
  ZoomIn,
  Eye,
  Zap,
  Cable,
  Gauge,
  Wifi,
  CheckCircle2,
  ArrowLeft,
  Check,
  Shield,
  ArrowDownCircle,
  BarChart,
  ExternalLink
} from "lucide-react";

// PDF URL for brochure
const PDF_URL = "/T&M April 2025.pdf";

// Modern Background Component
const ModernBackground = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-[-1] overflow-hidden bg-gradient-to-br from-yellow-50 to-gray-100">
      {/* Abstract shapes */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-yellow-100 rounded-bl-full opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-200 rounded-tr-full opacity-20"></div>

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
    </div>
  );
};

// Enhanced Hero Section Component
const HeroSection = ({ onRequestDemo, onViewBrochure }) => {
  return (
    <div className="relative py-20 md:py-32 overflow-hidden">
      {/* Hero Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-50 rounded-bl-[100px] transform -skew-x-12"></div>
        <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-10"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <div className="inline-block bg-yellow-400 py-1 px-3 rounded-full mb-2">
              <span className="text-sm font-semibold text-gray-900">Advanced Testing Equipment</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight">
              EARTH <span className="text-yellow-500">LOOP TESTERS</span>
            </h1>

            <p className="text-xl text-gray-900 leading-relaxed font-medium">
              Non-invasive measurement technology for efficient ground resistance testing without disconnecting the ground system.
            </p>

            <div className="pt-4 flex flex-wrap gap-4">
              <Button
                className="px-4 md:px-6 py-2 md:py-3 bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center space-x-2 text-sm md:text-base"
                onClick={onRequestDemo}
              >
                <span>Request Demo</span>
                <ArrowRight className="ml-2 h-4 md:h-5 w-4 md:w-5" />
              </Button>
              <Button
                className="px-4 md:px-6 py-2 md:py-3 bg-white border-2 border-yellow-500 text-gray-900 font-semibold rounded-lg shadow-sm transition duration-300 hover:bg-gray-50 flex items-center space-x-2 text-sm md:text-base"
                onClick={onViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-4 md:h-5 w-4 md:w-5" />
              </Button>
            </div>
          </motion.div>

          {/* Product Image */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative flex items-center justify-center h-full"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-2xl transform scale-110"></div>
            <motion.div
              animate={{
                y: [0, -15, 0],
              }}
              transition={{
                repeat: Infinity,
                duration: 3,
                ease: "easeInOut"
              }}
              className="relative z-10 flex justify-center"
            >
              <img
                src="/Earth loop testers/images-removebg-preview (1).png"
                alt="Earth Loop Tester"
                className="max-h-[800px] w-auto object-contain drop-shadow-2xl transform scale-[1.75] md:scale-[1.75] scale-[1.25]"
              />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// Animated Product Card
const ProductCard = ({
  product,
  onViewDetails,
  colors = {
    primary: 'yellow-500',
    secondary: 'yellow-50'
  }
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="group h-full"
    >
      <div className={`h-full rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-gray-100`}>
        {/* Product Image with Colored Background */}
        <div className={`relative p-4 md:p-8 flex justify-center items-center bg-${colors.secondary} h-64 md:h-80 overflow-hidden group-hover:bg-opacity-80 transition-all duration-700`}>
          <motion.img
            src={product.image}
            alt={product.name}
            className="h-56 md:h-72 w-auto object-contain z-10 drop-shadow-xl"
            whileHover={{ rotate: [-1, 1, -1], transition: { repeat: Infinity, duration: 2 } }}
          />
          <div className={`absolute top-4 left-4 bg-${colors.primary} text-white text-xs font-bold py-1 px-3 rounded-full`}>
            {product.id}
          </div>
        </div>

        {/* Product Content */}
        <div className="p-4 md:p-6 space-y-3 md:space-y-4">
          <h3 className="text-lg md:text-xl font-bold text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">
            {product.name}
          </h3>

          {/* Key Features */}
          <div className="space-y-2">
            {product.features.slice(0, 3).map((feature, idx) => (
              <div key={idx} className="flex items-start">
                <Check className="h-4 w-4 text-yellow-500 mt-1 mr-2 flex-shrink-0" />
                <span className="text-gray-800 text-xs md:text-sm font-medium">{feature}</span>
              </div>
            ))}
          </div>

          {/* Specs Badge */}
          <div className="flex flex-wrap gap-2 pt-2">
            <span className="inline-block bg-gray-100 rounded-full px-3 py-1 text-xs font-semibold text-gray-700">
              {product.measurements[0].split(':')[0]}
            </span>
            <span className="inline-block bg-gray-100 rounded-full px-3 py-1 text-xs font-semibold text-gray-700">
              {product.measurements[1].split(':')[0]}
            </span>
          </div>

          {/* View Details Button */}
          <Button
            onClick={() => onViewDetails(product.id)}
            className={`w-full mt-4 py-2 md:py-3 px-3 md:px-4 bg-${colors.primary} hover:bg-opacity-90 text-center font-semibold text-gray-900 rounded-lg transition-all duration-300 transform group-hover:-translate-y-1 flex items-center justify-center text-sm md:text-base`}
          >
            <span>View Details</span>
            <ChevronRight className="ml-1 h-3 md:h-4 w-3 md:w-4" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

// Feature Highlight Component
const FeatureHighlight = ({ icon, title, description }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      whileHover={{ y: -8, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
      className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 p-6 h-full border-b-4 border-yellow-400"
    >
      <div className="flex flex-col h-full">
        <div className="bg-gradient-to-br from-yellow-400 to-yellow-300 w-12 h-12 rounded-lg flex items-center justify-center mb-4 shadow-md">
          {icon}
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-3">{title}</h3>
        <p className="text-gray-800 flex-grow">{description}</p>
      </div>
    </motion.div>
  );
};

// SpecItem component for product details
const SpecItem = ({ icon, text }) => (
  <div className="flex items-center space-x-3 py-3 border-b border-yellow-100">
    <div className="bg-yellow-100 p-2 rounded-lg">
      {icon}
    </div>
    <span className="text-gray-800 font-medium">{text}</span>
  </div>
);

// Product Detail View with enhanced UI
const ProductDetailView = ({ product, onBackToList }) => {
  const [activeTab, setActiveTab] = useState("features");

  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
      <div className="p-6">
        <button
          onClick={onBackToList}
          className="flex items-center text-yellow-600 hover:text-yellow-700 mb-6"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Earth Loop Testers
        </button>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-50 rounded-full opacity-30 blur-xl transform scale-90"></div>
            <motion.div
              animate={{ y: [0, -10, 0] }}
              transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
              className="relative z-10 bg-yellow-50 p-6 rounded-lg flex items-center justify-center"
            >
              <img
                src={product.image}
                alt={product.name}
                className="max-h-64 w-auto object-contain drop-shadow-xl transform transition-transform duration-500 hover:scale-110"
              />
            </motion.div>
            <div className="mt-4 text-center">
              <span className="bg-yellow-500 px-3 py-1 rounded-full text-sm font-medium text-white">
                {product.id}
              </span>
            </div>
          </div>

          <div>
            <div className="bg-yellow-100 px-2 py-1 rounded text-xs font-medium text-yellow-800 inline-block mb-2">
              {product.category}
            </div>
            <h1 className="text-2xl font-bold mb-2 text-gray-900">{product.name}</h1>
            <div className="w-16 h-1 bg-yellow-500 mb-4"></div>
            <p className="text-gray-800 mb-6">{product.description}</p>

            <div className="flex border-b border-gray-200 mb-6">
              <button
                className={`py-2 px-4 font-medium ${
                  activeTab === "features"
                    ? "border-b-2 border-yellow-500 text-yellow-600"
                    : "text-gray-500 hover:text-gray-700"
                }`}
                onClick={() => setActiveTab("features")}
              >
                Salient Features
              </button>
              <button
                className={`py-2 px-4 font-medium ${
                  activeTab === "measurements"
                    ? "border-b-2 border-yellow-500 text-yellow-600"
                    : "text-gray-500 hover:text-gray-700"
                }`}
                onClick={() => setActiveTab("measurements")}
              >
                Measurements
              </button>
            </div>
          </div>
        </div>

        <div className="mt-8">
          {activeTab === "features" && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                <span className="w-2 h-6 bg-yellow-500 rounded-full mr-2"></span>
                Salient Features
              </h2>
              <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
                {product.features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="bg-yellow-50 p-3 md:p-4 rounded-lg flex items-start hover:bg-yellow-100 hover:shadow-md transition-all duration-300"
                  >
                    <div className="bg-yellow-500 p-1 rounded-full mr-2 md:mr-3 mt-0.5 flex-shrink-0">
                      <CheckCircle2 className="h-3 md:h-4 w-3 md:w-4 text-white" />
                    </div>
                    <span className="text-gray-800 text-sm md:text-base">{feature}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {activeTab === "measurements" && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                <span className="w-2 h-6 bg-yellow-500 rounded-full mr-2"></span>
                Measurements
              </h2>
              <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
                {product.measurements.map((measurement, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="bg-yellow-50 p-3 md:p-4 rounded-lg flex items-start hover:bg-yellow-100 hover:shadow-md transition-all duration-300"
                  >
                    <div className="bg-yellow-500 p-1 rounded-full mr-2 md:mr-3 mt-0.5 flex-shrink-0">
                      <Gauge className="h-3 md:h-4 w-3 md:w-4 text-white" />
                    </div>
                    <span className="text-gray-800 text-sm md:text-base">{measurement}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

// Applications Section with enhanced styling
const ApplicationsSection = () => {
  const applications = [
    {
      title: "Power Transmission",
      icon: <Zap className="h-6 w-6 text-yellow-600" />,
      description: "Verify grounding systems in power transmission towers and substations quickly without disconnection."
    },
    {
      title: "Telecommunications",
      icon: <Wifi className="h-6 w-6 text-yellow-600" />,
      description: "Test earth resistance in telecom towers and infrastructure for safety and signal integrity."
    },
    {
      title: "Lightning Protection",
      icon: <ZoomIn className="h-6 w-6 text-yellow-600" />,
      description: "Ensure lightning protection systems are properly grounded with non-invasive testing methods."
    },
    {
      title: "Railway Systems",
      icon: <Cable className="h-6 w-6 text-yellow-600" />,
      description: "Verify grounding in railway signaling and power distribution networks without service interruption."
    },
    {
      title: "Industrial Plants",
      icon: <Gauge className="h-6 w-6 text-yellow-600" />,
      description: "Test grounding systems in industrial facilities during operation without production downtime."
    },
    {
      title: "Electrical Installations",
      icon: <Shield className="h-6 w-6 text-yellow-600" />,
      description: "Verify and troubleshoot grounding in electrical systems with quick non-invasive measurements."
    }
  ];

  return (
    <div className="py-12 bg-gradient-to-br from-yellow-50 to-white rounded-2xl my-12 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Application Areas</h2>
          <p className="text-lg text-gray-800 max-w-3xl mx-auto font-medium">
            Our earth loop testers are used across various industries for efficient ground system testing
          </p>
          <div className="w-24 h-1 bg-yellow-500 mx-auto mt-4"></div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {applications.map((app, idx) => (
            <motion.div
              key={idx}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: idx * 0.1 }}
              viewport={{ once: true }}
              className="bg-white p-4 md:p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-2 border-b-4 border-yellow-400"
            >
              <div className="bg-yellow-100 w-12 md:w-16 h-12 md:h-16 rounded-lg flex items-center justify-center mb-4">
                {app.icon}
              </div>
              <h3 className="text-lg md:text-xl font-semibold mb-2 md:mb-3 text-gray-900">{app.title}</h3>
              <p className="text-sm md:text-base text-gray-800 font-medium">{app.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Contact Section Component
const ContactSection = ({ onContactClick }) => {
  return (
    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 py-8 px-4 rounded-2xl shadow-lg">
      <div className="max-w-4xl mx-auto text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Need Help Selecting the Right Earth Loop Tester?</h2>
        <p className="text-gray-800 mb-8 max-w-3xl mx-auto font-medium">
          Our team of experts can help you choose the right earth loop testing solution for your specific application needs. Contact us for personalized recommendations.
        </p>
        <Button
          className="inline-flex items-center px-6 md:px-8 py-3 md:py-4 bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 transform hover:-translate-y-1 text-sm md:text-base"
          onClick={onContactClick}
        >
          Contact Our Experts
          <ArrowRight className="ml-2 h-4 md:h-5 w-4 md:w-5" />
        </Button>
      </div>
    </div>
  );
};

// Earth Loop Testers data
const earthLoopTesters = [
  {
    id: "CA-6417",
    name: "Earth Loop Tester CA 6417",
    description: "Advanced earth loop tester with OLED display and wireless communication for efficient ground testing. Features non-invasive clamp-on technology for quick and easy measurements without disconnecting the ground system.",
    features: [
      "Ø35mm clamping diameter",
      "Large, 152-segment multi-function OLED display",
      "Display Counts: 1,500 counts (Loop ohmmeter) & 4,000-count display (Ammeter)",
      "Alarms",
      "HOLD & PRE-HOLD",
      "Auto power off",
      "Memory: 2,000 measurements",
      "Communication: Bluetooth",
      "PC interface & Android app"
    ],
    measurements: [
      "Loop Resistance Range: Upto 1,500 Ω",
      "Accuracy: ±1.5% ±0.01",
      "Frequency 2,083 Hz (Measurement frequency)",
      "50, 60, 128 or 2,083 Hz (Transposition freq.)",
      "Loop Inductance Range: Upto 500μH",
      "Accuracy: ±3% ±r",
      "Ground Voltage: Upto 75 V",
      "Current Range: Upto 39.99 A",
      "Accuracy: ±2% ±r"
    ],
    image: "/Earth loop testers/6417-removebg-preview.png",
    category: "Earth Loop Tester",
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    }
  },
  {
    id: "CA-6418",
    name: "Earth Loop Tester CA 6418",
    description: "Professional earth loop tester with oblong head design for enhanced accessibility and measurement accuracy. Perfect for testing grounding systems in tight spaces and complex installations.",
    features: [
      "Ø32/55mm max. clamping diameter (Oblong head)",
      "Large, 152-segment multi-function OLED display",
      "Alarms",
      "Automatic calibration of the jaw gap",
      "HOLD: Manual or automatic PRE-Hold",
      "Auto power off",
      "Memory: 300 measurements (Time/date-stamped)"
    ],
    measurements: [
      "Loop Resistance Range: Upto 1,200 Ω",
      "Accuracy: ±(1.5%R + 2r)",
      "Current Range: Upto 20 A",
      "Accuracy: ±(2 %R + 200μA)",
      "Ground Voltage Range: Upto 75 V"
    ],
    image: "/Earth loop testers/6418-removebg-preview.png",
    category: "Earth Loop Tester",
    colors: {
      primary: 'yellow-600',
      secondary: 'yellow-100'
    }
  }
];

// Main Earth Loop Testers component
const EarthLoopTesters = () => {
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeProductType, setActiveProductType] = useState("CA-6417");
  const navigate = useNavigate();
  const location = useLocation();
  const productDetailsRef = useRef(null);

  // Effect to check URL parameters for initial tab and product type
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    const product = params.get('product');

    if (tab) setActiveTab(tab);
    if (product) {
      setActiveProductType(product);
      const foundProduct = earthLoopTesters.find(p => p.id === product);
      if (foundProduct) {
        setSelectedProduct(foundProduct);
        // If we're showing details, scroll to the product details after a short delay
        if (tab === "details") {
          setTimeout(() => {
            const productDetailsElement = document.getElementById("product-details-section");
            if (productDetailsElement) {
              productDetailsElement.scrollIntoView({ behavior: 'auto', block: 'start' });
            }
          }, 100);
        }
      }
    }
  }, [location]);

  // Find product by ID
  const findProductById = (id) => {
    return earthLoopTesters.find(product => product.id === id);
  };  // Handle view details click
  const handleViewDetails = (id) => {
    const product = findProductById(id);
    if (product) {
      setSelectedProduct(product);
      setActiveTab("details");
      setActiveProductType(id);
      navigate(`?tab=details&product=${id}`, { replace: true });

      // Wait a moment for the UI to update, then scroll directly to the product details section
      setTimeout(() => {
        const productDetailsElement = document.getElementById("product-details-section");
        if (productDetailsElement) {
          productDetailsElement.scrollIntoView({ behavior: 'auto', block: 'start' });
        }
      }, 100);
    }
  };

  // Handle back to list
  const handleBackToList = () => {
    setSelectedProduct(null);
    setActiveTab("overview");
    navigate(`?tab=overview`, { replace: true });
  };

  // Handler for Request Demo button
  const handleRequestDemo = () => {
    navigate("/contact/sales");
  };

  // Handler for View Brochure button
  const handleViewBrochure = () => {
    window.open(PDF_URL, '_blank');
  };

  // Navigation tabs data
  const navTabs = [
    { id: "overview", label: "Overview", icon: <Gauge className="h-5 w-5" /> },
    { id: "details", label: "Product Details", icon: <Zap className="h-5 w-5" /> },
    { id: "applications", label: "Applications", icon: <Shield className="h-5 w-5" /> }
  ];

  return (
    <PageLayout
      title="Earth Loop Testers"
      subtitle="Our specialized range of clamp-on earth loop testers for non-invasive ground resistance measurement"
      category="measure"
    >
      {/* Modern Background */}
      <ModernBackground />

      {/* Premium Modern Navigation Tabs - Responsive Design */}
      <div className="sticky top-0 z-30 bg-white shadow-lg border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4">
          {/* Desktop Navigation */}
          <div className="hidden md:flex justify-center py-2">
            <div className="bg-gray-50 p-1.5 rounded-full flex shadow-sm">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "details" && selectedProduct) {
                      navigate(`?tab=${tab.id}&product=${selectedProduct.id}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={cn(
                    "relative px-6 py-2.5 font-medium rounded-full transition-all duration-300 flex items-center mx-1 overflow-hidden",
                    activeTab === tab.id
                      ? "bg-gradient-to-r from-yellow-500 to-yellow-400 text-white shadow-md transform -translate-y-0.5"
                      : "text-gray-700 hover:bg-yellow-50 hover:text-yellow-500"
                  )}
                >
                  <div className="flex items-center relative z-10">
                    <span className="mr-2">{tab.icon}</span>
                    <span>{tab.label}</span>
                  </div>
                  {activeTab === tab.id && (
                    <motion.div
                      layoutId="navIndicator"
                      className="absolute inset-0 bg-gradient-to-r from-yellow-500 to-yellow-400 -z-0"
                      initial={false}
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden py-2 flex justify-between items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-gray-700 hover:text-yellow-500 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <span className="font-semibold text-gray-900 text-lg">
              {navTabs.find(tab => tab.id === activeTab)?.label}
            </span>

            <div className="w-6"></div> {/* Spacer for balanced layout */}
          </div>

          {/* Mobile Menu Dropdown */}
          <div className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-60' : 'max-h-0'}`}>
            <div className="bg-white rounded-lg shadow-lg p-2 mb-2">
              {navTabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    if (tab.id === "details" && selectedProduct) {
                      navigate(`?tab=${tab.id}&product=${selectedProduct.id}`, { replace: true });
                    } else {
                      navigate(`?tab=${tab.id}`, { replace: true });
                    }
                    setIsMobileMenuOpen(false);
                  }}
                  className={`w-full text-left px-4 py-3 rounded-lg my-1 flex items-center ${
                    activeTab === tab.id
                      ? "bg-yellow-50 text-yellow-600 font-medium"
                      : "text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <span className="mr-3">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {activeTab === "overview" && (
        <>
          {/* Hero Section */}
          <HeroSection
            onRequestDemo={handleRequestDemo}
            onViewBrochure={handleViewBrochure}
          />

          {/* Key Features Section */}
          <div className="py-16 bg-gradient-to-br from-yellow-50 to-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-12">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-3xl font-bold text-yellow-600 mb-4">Earth Loop Tester Advantages</h2>
                  <p className="mt-4 text-lg text-gray-800 max-w-3xl mx-auto font-medium">
                    Our earth loop testers offer non-invasive measurement capabilities with industry-leading accuracy
                  </p>
                </motion.div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8">
                <FeatureHighlight
                  icon={<ZoomIn className="h-5 md:h-6 w-5 md:w-6 text-white" />}
                  title="Non-Invasive Testing"
                  description="Measure earth resistance without disconnecting the ground system, saving time and maintaining safety."
                />

                <FeatureHighlight
                  icon={<Gauge className="h-5 md:h-6 w-5 md:w-6 text-white" />}
                  title="High Precision"
                  description="Advanced clamp-on technology provides accurate measurements of loop resistance with industry-leading precision."
                />

                <FeatureHighlight
                  icon={<Wifi className="h-5 md:h-6 w-5 md:w-6 text-white" />}
                  title="Connectivity"
                  description="Built-in Bluetooth, mobile app support, and data logging functionality for comprehensive testing and analysis."
                />
              </div>
            </div>
          </div>

          {/* Products Section */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-1 rounded-full text-sm font-semibold mb-4">
                PROFESSIONAL SERIES
              </span>
              <h2 className="text-4xl font-bold mb-6 text-gray-900">
                Our Earth Loop Tester Range
              </h2>
              <p className="max-w-3xl mx-auto text-gray-800 text-lg font-medium">
                Choose the perfect earth loop tester for your grounding measurement needs.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
              {earthLoopTesters.map(product => (
                <ProductCard
                  key={product.id}
                  product={product}
                  onViewDetails={handleViewDetails}
                  colors={product.colors}
                />
              ))}
            </div>
          </div>

          {/* Applications Section */}
          <ApplicationsSection />

          {/* Contact Section */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </>
      )}

      {activeTab === "details" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Product Selector/Tabs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-r from-yellow-50 to-white rounded-xl shadow-md p-6 mb-8 relative overflow-hidden"
          >
            <div className="absolute top-0 right-0 w-64 h-64 bg-yellow-200 rounded-full opacity-20 transform translate-x-1/2 -translate-y-1/2 blur-3xl"></div>

            <h2 className="text-xl font-bold text-gray-900 mb-4 relative z-10">
              Select <span className="text-yellow-500">Earth Loop Tester</span> Model
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 relative z-10">
              {earthLoopTesters.map((tester) => (
                <motion.button
                  key={tester.id}
                  whileHover={{ y: -3, boxShadow: "0 8px 20px -5px rgba(0, 0, 0, 0.1)" }}
                  whileTap={{ y: 0 }}                  onClick={() => {
                    setSelectedProduct(tester);
                    setActiveProductType(tester.id);
                    navigate(`?tab=details&product=${tester.id}`, { replace: true });

                    // Wait a moment for the UI to update, then scroll directly to the product details section
                    setTimeout(() => {
                      const productDetailsElement = document.getElementById("product-details-section");
                      if (productDetailsElement) {
                        productDetailsElement.scrollIntoView({ behavior: 'auto', block: 'start' });
                      }
                    }, 100);
                  }}
                  className={`relative rounded-lg transition-all duration-300 overflow-hidden ${
                    selectedProduct?.id === tester.id
                      ? "ring-1 ring-yellow-400"
                      : "hover:ring-1 hover:ring-yellow-300"
                  }`}
                >
                  <div className={`h-full py-4 px-3 flex flex-col items-center text-center ${
                    selectedProduct?.id === tester.id
                      ? "bg-yellow-500 text-black"
                      : "bg-white hover:bg-yellow-50"
                  }`}>
                    <div className="mb-2">
                      <Gauge className={`h-8 w-8 ${selectedProduct?.id === tester.id ? "text-black" : "text-yellow-500"}`} />
                    </div>

                    <h3 className={`text-lg font-bold mb-1 ${selectedProduct?.id === tester.id ? "text-black" : "text-gray-900"}`}>
                      {tester.name.split(' ').pop()} {/* Show only model number */}
                    </h3>

                    <div className={`text-xs ${selectedProduct?.id === tester.id ? "text-black" : "text-gray-500"}`}>
                      {tester.features[0]} {/* Show first feature */}
                    </div>

                    {selectedProduct?.id === tester.id && (
                      <div className="mt-2 bg-white bg-opacity-50 rounded-full px-3 py-0.5 text-xs font-semibold text-black">
                        Selected
                      </div>
                    )}
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>          {/* Product Detail View */}
          {selectedProduct && (
            <div id="product-details-section" ref={productDetailsRef}>
              <ProductDetailView
                product={selectedProduct}
                onBackToList={handleBackToList}
              />
            </div>
          )}

          {/* Comparison Table */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden mt-8">
            <div className="bg-gradient-to-r from-yellow-500 to-yellow-400 p-4">
              <h3 className="text-2xl font-bold text-center text-white">Model Comparison</h3>
            </div>
            <div className="p-6 overflow-x-auto -mx-6 md:mx-0">
              <table className="w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-6 py-3 bg-yellow-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider rounded-tl-lg">Feature</th>
                    {earthLoopTesters.map((tester, idx) => (
                      <th key={idx} className={`px-6 py-3 bg-yellow-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider ${idx === earthLoopTesters.length - 1 ? 'rounded-tr-lg' : ''}`}>
                        {tester.id}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {[
                    { name: 'Clamping Diameter', values: ['Ø35mm', 'Ø32/55mm (Oblong)'] },
                    { name: 'Display', values: ['OLED, 152-segment', 'OLED, 152-segment'] },
                    { name: 'Loop Resistance', values: ['Up to 1,500 Ω', 'Up to 1,200 Ω'] },
                    { name: 'Current Range', values: ['Up to 39.99 A', 'Up to 20 A'] },
                    { name: 'Memory', values: ['2,000 measurements', '300 measurements'] },
                    { name: 'Communication', values: ['Bluetooth, PC, Android', 'N/A'] }
                  ].map((feature, idx) => (
                    <motion.tr
                      key={idx}
                      initial={{ opacity: 0, y: 5 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: idx * 0.05 }}
                      viewport={{ once: true }}
                      className={idx % 2 === 0 ? 'bg-white hover:bg-yellow-50 transition-colors duration-200' : 'bg-gray-50 hover:bg-yellow-50 transition-colors duration-200'}
                    >
                      <td className="px-6 py-4 whitespace-normal md:whitespace-nowrap text-sm font-medium text-gray-900">{feature.name}</td>
                      {feature.values.map((value, i) => (
                        <td key={i} className="px-6 py-4 whitespace-normal md:whitespace-nowrap text-sm text-gray-800 text-center">
                          {value}
                        </td>
                      ))}
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Enhanced Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className="mt-20"
          >
            <ContactSection onContactClick={handleRequestDemo} />
          </motion.div>
        </div>
      )}

      {activeTab === "applications" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h1 className="text-3xl font-bold text-gray-900 mb-4">Applications</h1>
              <p className="text-lg text-gray-800 max-w-3xl mx-auto">
                Earth loop testers are versatile instruments designed for a wide range of professional applications in ground testing
              </p>
            </motion.div>
          </div>

          <ApplicationsSection />

          {/* Industry Applications */}
          <div className="mt-16 bg-white rounded-2xl shadow-lg p-4 md:p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Industry-Specific Solutions</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 mb-8">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-3 md:space-x-4"
              >
                <div className="bg-yellow-100 p-2 md:p-3 rounded-lg text-yellow-600 flex-shrink-0">
                  <Zap className="h-5 md:h-6 w-5 md:w-6" />
                </div>
                <div>
                  <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-1 md:mb-2">Power Distribution</h3>
                  <p className="text-gray-800 font-medium text-sm md:text-base">
                    Verify grounding systems in transformers, substations, and distribution networks for electrical safety compliance.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex items-start space-x-3 md:space-x-4"
              >
                <div className="bg-yellow-100 p-2 md:p-3 rounded-lg text-yellow-600 flex-shrink-0">
                  <BarChart className="h-5 md:h-6 w-5 md:w-6" />
                </div>
                <div>
                  <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-1 md:mb-2">Preventive Maintenance</h3>
                  <p className="text-gray-800 font-medium text-sm md:text-base">
                    Schedule regular ground resistance testing to identify potential faults before they cause equipment failures.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-3 md:space-x-4"
              >
                <div className="bg-yellow-100 p-2 md:p-3 rounded-lg text-yellow-600 flex-shrink-0">
                  <Shield className="h-5 md:h-6 w-5 md:w-6" />
                </div>
                <div>
                  <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-1 md:mb-2">Building Inspections</h3>
                  <p className="text-gray-800 font-medium text-sm md:text-base">
                    Verify grounding systems in commercial buildings to ensure compliance with electrical safety standards.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-3 md:space-x-4"
              >
                <div className="bg-yellow-100 p-2 md:p-3 rounded-lg text-yellow-600 flex-shrink-0">
                  <Eye className="h-5 md:h-6 w-5 md:w-6" />
                </div>
                <div>
                  <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-1 md:mb-2">Troubleshooting</h3>
                  <p className="text-gray-800 font-medium text-sm md:text-base">
                    Quickly identify and resolve ground faults in electrical systems without disrupting operations.
                  </p>
                </div>
              </motion.div>
            </div>

            {/* View Brochure button */}
            <div className="flex justify-center mt-8">
              <Button
                className="px-4 md:px-6 py-2 md:py-3 bg-gradient-to-r from-yellow-500 to-yellow-400 hover:from-yellow-600 hover:to-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center space-x-2 text-sm md:text-base"
                onClick={handleViewBrochure}
              >
                <span>View Brochure</span>
                <FileText className="ml-2 h-4 md:h-5 w-4 md:w-5" />
              </Button>
            </div>
          </div>

          {/* Contact Section */}
          <div className="mt-16">
            <ContactSection onContactClick={handleRequestDemo} />
          </div>
        </div>
      )}

      {/* No PDF Viewer Modal - Using direct link instead */}
    </PageLayout>
  );
};

export default EarthLoopTesters;