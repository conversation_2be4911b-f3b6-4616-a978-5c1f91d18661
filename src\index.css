
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 217 91% 60%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 217 91% 60%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 222 47% 11%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5% 26%;
    --sidebar-primary: 240 6% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 5% 96%;
    --sidebar-accent-foreground: 240 6% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 91% 60%;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 217 91% 60%;
    --primary-foreground: 222 47% 11%;

    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 33% 17%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 212 27% 84%;

    --sidebar-background: 240 6% 10%;
    --sidebar-foreground: 240 5% 96%;
    --sidebar-primary: 224 76% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4% 16%;
    --sidebar-accent-foreground: 240 5% 96%;
    --sidebar-border: 240 4% 16%;
    --sidebar-ring: 217 91% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html, body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    background-color: #000000;
    /* Improve touch scrolling on mobile */
    -webkit-overflow-scrolling: touch;
    /* Prevent zoom on input focus on iOS */
    -webkit-text-size-adjust: 100%;
  }

  body {
    @apply bg-black text-foreground tracking-tight antialiased;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02";
    font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    /* Improve text rendering on mobile */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Fallback to system fonts if SF Pro Display is not available */
  @font-face {
    font-family: 'SF Pro Display';
    src: local('SF Pro Display'), local('SFProDisplay-Regular'), local('SF Pro Text'), local('SFProText-Regular'), local('Segoe UI'), local('Helvetica Neue');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'SF Pro Display';
    src: local('SF Pro Display Medium'), local('SFProDisplay-Medium'), local('SF Pro Text Medium'), local('SFProText-Medium'), local('Segoe UI Semibold'), local('Helvetica Neue Medium');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'SF Pro Display';
    src: local('SF Pro Display Bold'), local('SFProDisplay-Bold'), local('SF Pro Text Bold'), local('SFProText-Bold'), local('Segoe UI Bold'), local('Helvetica Neue Bold');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }
}

/* Animations and Effects */
@layer components {
  .glass-panel {
    @apply bg-white/80 dark:bg-black/30 backdrop-blur-md border border-white/20 dark:border-white/10 shadow-glass;
  }

  .section-transition {
    @apply transition-all duration-700 ease-out;
  }

  .parallax-slow {
    transition: transform 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .parallax-medium {
    transition: transform 0.35s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .parallax-fast {
    transition: transform 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .hover-scale {
    @apply transition-transform duration-300 ease-out hover:scale-105;
  }

  /* Mobile-specific utilities */
  .touch-manipulation {
    touch-action: manipulation;
  }

  .safe-area-inset {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* 3D Chatbot Button Animations */
  .chatbot-siri-pulse {
    animation: siri-pulse 2s ease-in-out infinite;
  }

  .chatbot-breathing {
    animation: breathing 3s ease-in-out infinite;
  }

  .chatbot-glow {
    animation: glow-pulse 2.5s ease-in-out infinite alternate;
  }
}

/* Keyframes for 3D Chatbot Animations */
@keyframes siri-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
}

@keyframes breathing {
  0%, 100% {
    transform: scale(1) translateZ(0);
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  }
  50% {
    transform: scale(1.02) translateZ(2px);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
  }
}

@keyframes glow-pulse {
  0% {
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 40px rgba(59, 130, 246, 0.2),
      0 0 60px rgba(59, 130, 246, 0.1);
  }
  100% {
    box-shadow:
      0 0 30px rgba(59, 130, 246, 0.5),
      0 0 60px rgba(59, 130, 246, 0.3),
      0 0 90px rgba(59, 130, 246, 0.2);
  }

  /* Responsive text utilities */
  .text-responsive-xs {
    @apply text-xs sm:text-sm md:text-base;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base md:text-lg;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg md:text-xl;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl md:text-4xl;
  }

  /* Container utilities for better responsive design */
  .container-responsive {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .container-tight {
    @apply w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* Enhanced mobile-specific optimizations */
@media (max-width: 640px) {
  /* Improve button touch targets on mobile */
  button, a[role="button"], .btn {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem 1rem;
  }

  /* Enhanced form optimization for mobile */
  input, textarea, select {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    width: 100%;
    box-sizing: border-box;
  }

  /* Better spacing for mobile */
  .mobile-spacing {
    padding: 1rem;
  }

  .mobile-spacing-sm {
    padding: 0.5rem;
  }

  .mobile-spacing-lg {
    padding: 1.5rem;
  }

  /* Optimize images for mobile */
  img {
    max-width: 100%;
    height: auto;
    object-fit: cover;
  }

  /* Fix text alignment issues on mobile */
  .text-justify {
    text-align: left;
  }

  /* Prevent horizontal overflow */
  * {
    max-width: 100%;
    box-sizing: border-box;
  }

  /* Better line height for readability */
  p, div, span {
    line-height: 1.6;
  }

  /* Enhanced heading sizes for mobile */
  h1 {
    font-size: 1.875rem !important; /* 30px */
    line-height: 1.2;
  }

  h2 {
    font-size: 1.5rem !important; /* 24px */
    line-height: 1.3;
  }

  h3 {
    font-size: 1.25rem !important; /* 20px */
    line-height: 1.4;
  }

  /* Ensure proper spacing between elements */
  .space-y-4 > * + * {
    margin-top: 1rem !important;
  }

  .space-y-6 > * + * {
    margin-top: 1.5rem !important;
  }

  .space-y-8 > * + * {
    margin-top: 2rem !important;
  }

  /* Fix grid layouts on mobile */
  .grid {
    gap: 1rem !important;
  }

  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }

  .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }

  .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  /* Ensure flex items don't overflow */
  .flex {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  /* Better padding for containers */
  .container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }

  /* Enhanced card layouts for mobile */
  .card, [class*="card"] {
    margin-bottom: 1rem;
    border-radius: 0.75rem;
    overflow: hidden;
  }

  /* Better navigation spacing */
  nav {
    padding: 0.5rem 1rem;
  }

  /* Optimize modal/dialog for mobile */
  .modal, .dialog, [role="dialog"] {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
  }

  /* Enhanced table responsiveness */
  table {
    font-size: 0.875rem;
  }

  th, td {
    padding: 0.5rem 0.25rem;
  }

  /* Better form layout */
  .form-group {
    margin-bottom: 1rem;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
  }
}

/* Enhanced tablet-specific optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
  /* Enhanced container spacing for tablet */
  .container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  /* Better grid layouts for tablet */
  .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1.5rem;
  }

  .grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1.5rem;
  }

  .grid-cols-6 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1.5rem;
  }

  .tablet-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  /* Optimize touch targets for tablet */
  button, a[role="button"], .btn {
    min-height: 40px;
    min-width: 40px;
    padding: 0.625rem 1.25rem;
  }

  /* Enhanced typography for tablet */
  h1 {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  h2 {
    font-size: 2rem;
    line-height: 1.3;
  }

  h3 {
    font-size: 1.5rem;
    line-height: 1.4;
  }

  /* Better spacing for tablet */
  .space-y-6 > * + * {
    margin-top: 2rem;
  }

  .space-y-8 > * + * {
    margin-top: 2.5rem;
  }

  /* Enhanced card layouts for tablet */
  .card, [class*="card"] {
    margin-bottom: 1.5rem;
    padding: 1.5rem;
  }

  /* Better navigation for tablet */
  nav {
    padding: 1rem 2rem;
  }

  /* Form optimization for tablet */
  input, textarea, select {
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
  }

  /* Table optimization for tablet */
  table {
    font-size: 1rem;
  }

  th, td {
    padding: 0.75rem 1rem;
  }
}

/* Enhanced desktop optimizations */
@media (min-width: 1025px) {
  /* Enhanced container spacing for desktop */
  .container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  /* Better grid layouts for desktop */
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 2rem;
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 2rem;
  }

  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
    gap: 2rem;
  }

  .grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
    gap: 2rem;
  }

  .desktop-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  /* Enhanced typography for desktop */
  h1 {
    font-size: 3.5rem;
    line-height: 1.1;
  }

  h2 {
    font-size: 2.75rem;
    line-height: 1.2;
  }

  h3 {
    font-size: 2rem;
    line-height: 1.3;
  }

  /* Better spacing for desktop */
  .space-y-8 > * + * {
    margin-top: 3rem;
  }

  .space-y-12 > * + * {
    margin-top: 4rem;
  }

  /* Enhanced card layouts for desktop */
  .card, [class*="card"] {
    margin-bottom: 2rem;
    padding: 2rem;
  }

  /* Better navigation for desktop */
  nav {
    padding: 1.5rem 3rem;
  }

  /* Form optimization for desktop */
  input, textarea, select {
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }

  /* Table optimization for desktop */
  table {
    font-size: 1rem;
  }

  th, td {
    padding: 1rem 1.5rem;
  }

  /* Enhanced button styling for desktop */
  button, a[role="button"], .btn {
    padding: 0.75rem 1.5rem;
    transition: all 0.2s ease;
  }

  button:hover, a[role="button"]:hover, .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Optimize for retina displays */
  img {
    image-rendering: -webkit-optimize-contrast;
  }
}

/* Additional responsive utilities for better mobile experience */
@layer utilities {
  /* Responsive text alignment */
  .text-responsive-center {
    @apply text-center sm:text-left;
  }

  .text-responsive-left {
    @apply text-left;
  }

  /* Enhanced responsive text sizes with better scaling */
  .text-responsive-xs {
    @apply text-xs sm:text-sm md:text-base;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base md:text-lg lg:text-xl;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg md:text-xl lg:text-2xl;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl lg:text-3xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl lg:text-4xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl;
  }

  .text-responsive-3xl {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl;
  }

  /* Enhanced responsive spacing utilities */
  .spacing-responsive-xs {
    @apply space-y-1 sm:space-y-2 md:space-y-3;
  }

  .spacing-responsive-sm {
    @apply space-y-2 sm:space-y-3 md:space-y-4 lg:space-y-5;
  }

  .spacing-responsive-md {
    @apply space-y-3 sm:space-y-4 md:space-y-6 lg:space-y-8;
  }

  .spacing-responsive-lg {
    @apply space-y-4 sm:space-y-6 md:space-y-8 lg:space-y-10;
  }

  .spacing-responsive-xl {
    @apply space-y-6 sm:space-y-8 md:space-y-10 lg:space-y-12;
  }

  /* Enhanced responsive padding utilities */
  .padding-responsive-xs {
    @apply p-1 sm:p-2 md:p-3;
  }

  .padding-responsive-sm {
    @apply p-2 sm:p-3 md:p-4 lg:p-5;
  }

  .padding-responsive-md {
    @apply p-3 sm:p-4 md:p-6 lg:p-8;
  }

  .padding-responsive-lg {
    @apply p-4 sm:p-6 md:p-8 lg:p-10;
  }

  .padding-responsive-xl {
    @apply p-6 sm:p-8 md:p-10 lg:p-12;
  }

  /* Enhanced responsive margin utilities */
  .margin-responsive-xs {
    @apply m-1 sm:m-2 md:m-3;
  }

  .margin-responsive-sm {
    @apply m-2 sm:m-3 md:m-4 lg:m-5;
  }

  .margin-responsive-md {
    @apply m-3 sm:m-4 md:m-6 lg:m-8;
  }

  .margin-responsive-lg {
    @apply m-4 sm:m-6 md:m-8 lg:m-10;
  }

  /* Enhanced responsive container utilities */
  .container-responsive {
    @apply w-full px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-20;
  }

  .container-responsive-tight {
    @apply w-full px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10;
  }

  .container-responsive-wide {
    @apply w-full px-6 sm:px-8 md:px-12 lg:px-16 xl:px-20 2xl:px-24;
  }

  /* Enhanced responsive grid utilities */
  .grid-responsive-1-2 {
    @apply grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6;
  }

  .grid-responsive-1-2-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8;
  }

  .grid-responsive-1-2-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8;
  }

  .grid-responsive-2-3-4 {
    @apply grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6;
  }

  .grid-responsive-auto {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 sm:gap-6;
  }

  /* Enhanced responsive flex utilities */
  .flex-responsive-col {
    @apply flex flex-col sm:flex-row gap-4 sm:gap-6;
  }

  .flex-responsive-row {
    @apply flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4;
  }

  .flex-responsive-wrap {
    @apply flex flex-wrap gap-2 sm:gap-3 md:gap-4;
  }

  /* Enhanced responsive width utilities */
  .width-responsive-full {
    @apply w-full sm:w-auto;
  }

  .width-responsive-auto {
    @apply w-auto;
  }

  .width-responsive-fit {
    @apply w-full sm:w-fit;
  }

  /* Enhanced anti-overlap utilities */
  .no-overlap {
    position: relative;
    z-index: 1;
    overflow: hidden;
  }

  .safe-spacing {
    margin: 0.5rem;
    padding: 0.5rem;
  }

  .prevent-overflow {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Enhanced mobile touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    touch-action: manipulation;
  }

  .touch-target-lg {
    min-height: 48px;
    min-width: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    touch-action: manipulation;
  }

  /* Enhanced responsive image utilities */
  .img-responsive {
    width: 100%;
    height: auto;
    max-width: 100%;
    object-fit: cover;
  }

  .img-responsive-contain {
    width: 100%;
    height: auto;
    max-width: 100%;
    object-fit: contain;
  }

  .img-responsive-square {
    width: 100%;
    aspect-ratio: 1;
    object-fit: cover;
  }

  .img-responsive-landscape {
    width: 100%;
    aspect-ratio: 16/9;
    object-fit: cover;
  }

  .img-responsive-portrait {
    width: 100%;
    aspect-ratio: 3/4;
    object-fit: cover;
  }

  /* Full container image coverage - no gaps */
  .img-full-cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    min-width: 100%;
    min-height: 100%;
  }

  /* Enhanced image utilities for better visual prominence */
  .img-enhanced {
    filter: contrast(1.05) saturate(1.05) brightness(1.02);
    transition: filter 0.3s ease, transform 0.3s ease;
  }

  .img-enhanced:hover {
    filter: contrast(1.1) saturate(1.1) brightness(1.05);
  }

  /* Radial gradient utility for vignette effects */
  .bg-radial-gradient {
    background: radial-gradient(circle at center, var(--tw-gradient-stops));
  }

  /* Enhanced shadow utilities */
  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .shadow-4xl {
    box-shadow: 0 50px 100px -20px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .shadow-responsive {
    @apply shadow-sm sm:shadow-md md:shadow-lg lg:shadow-xl;
  }

  /* Enhanced text wrapping utilities */
  .break-words {
    word-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
  }

  .break-words-safe {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    max-width: 100%;
  }

  /* Prevent text overflow */
  .text-ellipsis-responsive {
    @apply truncate sm:text-clip;
  }

  /* Enhanced responsive height utilities */
  .height-responsive-screen {
    @apply h-screen sm:h-auto;
  }

  .height-responsive-auto {
    @apply h-auto;
  }

  .min-height-responsive {
    @apply min-h-[200px] sm:min-h-[250px] md:min-h-[300px] lg:min-h-[350px];
  }
}

.shimmer {
  @apply relative overflow-hidden before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent;
}

/* Footer divider */
.footer-divider {
  @apply w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent;
}
